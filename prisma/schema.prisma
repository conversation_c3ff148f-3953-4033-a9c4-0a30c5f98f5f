generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum PostType {
  ARTICLE
  VIDEOYT
  VIDEOOTHER
}

model Article {
  id             Int      @id @default(autoincrement())
  title          String
  post_type      PostType @default(ARTICLE)
  content        String
  summary        String?
  summarization_vendor String?
  slug           String
  url            String   @unique
  author         String?
  source_id      Int
  source         Source?  @relation(fields: [source_id], references: [id])
  tags           String[]
  published_date DateTime
  thumbnail_key  String?
  views          Int?     @default(0)
  votes          Int?     @default(0)
  video_id       String?

  // Relations for voting and bookmarking
  userVotes      Vote[]
  bookmarks      Bookmark[]

  // createdAt   DateTime @default(now())
  @@index([source_id])
  @@index([published_date])
  @@index([votes])
  @@map("articles")
}

model Source {
  id       Int       @id @default(autoincrement())
  title    String
  site     String
  rss_url  String
  articles Article[]

  @@map("sources")
}

// Payload CMS will handle the users table
// We'll reference it by ID for our voting/bookmarking system

model Vote {
  id        String   @id @default(cuid())
  userId    Int?     // Optional - references Payload users.id
  articleId Int
  ipAddress String?  // For anonymous vote tracking
  createdAt DateTime @default(now())

  article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@unique([ipAddress, articleId])
  @@map("votes")
}

model Bookmark {
  id        String   @id @default(cuid())
  userId    Int      // References Payload users.id
  articleId Int
  createdAt DateTime @default(now())

  article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@map("bookmarks")
}