generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Article {
  id                            Int                             @id @default(autoincrement())
  title                         String                          @db.VarChar
  content                       Json?
  summary                       String?                         @db.VarChar
  summarization_vendor          String?                         @db.VarChar
  slug                          String                          @unique(map: "articles_slug_idx") @db.VarChar
  url                           String                          @unique(map: "articles_url_idx") @db.VarChar
  author                        String?                         @db.VarChar
  tags                          String?                         @db.VarChar
  published_date                DateTime                        @db.Timestamptz(3)
  thumbnail_key                 String?                         @db.VarChar
  views                         Int                             @default(0)
  votes                         Int                             @default(0)
  video_id                      String?                         @db.VarChar
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  featured                      Boolean?                        @default(false)
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  status                        enum_articles_status            @default(draft)
  post_type                     enum_articles_post_type         @default(ARTICLE)
  source_id                     Int
  source                        Source                          @relation(fields: [source_id], references: [id], onDelete: SetNull, onUpdate: NoAction, map: "articles_source_id_sources_id_fk")
  bookmarks                     Bookmark[]
  payload_locked_documents_rels payload_locked_documents_rels[]
  vote_records                  Vote[]

  @@index([created_at])
  @@index([featured])
  @@index([post_type])
  @@index([published_date])
  @@index([published_date, status])
  @@index([source_id])
  @@index([status])
  @@index([updated_at])
  @@index([votes])
  @@index([views])
  @@map("articles")
}

model Bookmark {
  id        String  @id
  articleId Int
  createdAt DateTime @default(now())
  userId    Int
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@index([articleId])
  @@index([createdAt])
  @@index([userId])
  @@map("bookmarks")
}

model media {
  id                            Int                             @id @default(autoincrement())
  alt                           String                          @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  url                           String?                         @db.VarChar
  thumbnail_u_r_l               String?                         @db.VarChar
  filename                      String?                         @unique(map: "media_filename_idx") @db.VarChar
  mime_type                     String?                         @db.VarChar
  filesize                      Decimal?                        @db.Decimal
  width                         Decimal?                        @db.Decimal
  height                        Decimal?                        @db.Decimal
  focal_x                       Decimal?                        @db.Decimal
  focal_y                       Decimal?                        @db.Decimal
  sizes_thumbnail_url           String?                         @db.VarChar
  sizes_thumbnail_width         Decimal?                        @db.Decimal
  sizes_thumbnail_height        Decimal?                        @db.Decimal
  sizes_thumbnail_mime_type     String?                         @db.VarChar
  sizes_thumbnail_filesize      Decimal?                        @db.Decimal
  sizes_thumbnail_filename      String?                         @db.VarChar
  sizes_card_url                String?                         @db.VarChar
  sizes_card_width              Decimal?                        @db.Decimal
  sizes_card_height             Decimal?                        @db.Decimal
  sizes_card_mime_type          String?                         @db.VarChar
  sizes_card_filesize           Decimal?                        @db.Decimal
  sizes_card_filename           String?                         @db.VarChar
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([created_at])
  @@index([sizes_card_filename], map: "media_sizes_card_sizes_card_filename_idx")
  @@index([sizes_thumbnail_filename], map: "media_sizes_thumbnail_sizes_thumbnail_filename_idx")
  @@index([updated_at])
}

model payload_locked_documents {
  id                            Int                             @id @default(autoincrement())
  global_slug                   String?                         @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([created_at])
  @@index([global_slug])
  @@index([updated_at])
}

model payload_locked_documents_rels {
  id                       Int                      @id @default(autoincrement())
  order                    Int?
  parent_id                Int
  path                     String                   @db.VarChar
  users_id                 Int?
  articles_id              Int?
  media_id                 Int?
  sources_id               Int?
  articles                 Article?                 @relation(fields: [articles_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_articles_fk")
  media                    media?                   @relation(fields: [media_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_media_fk")
  payload_locked_documents payload_locked_documents @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_parent_fk")
  sources                  Source?                  @relation(fields: [sources_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_sources_fk")
  users                    User?                    @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_users_fk")

  @@index([articles_id])
  @@index([media_id])
  @@index([order])
  @@index([parent_id], map: "payload_locked_documents_rels_parent_idx")
  @@index([path])
  @@index([sources_id])
  @@index([users_id])
}

model payload_migrations {
  id         Int      @id @default(autoincrement())
  name       String?  @db.VarChar
  batch      Decimal? @db.Decimal
  updated_at DateTime @default(now()) @db.Timestamptz(3)
  created_at DateTime @default(now()) @db.Timestamptz(3)

  @@index([created_at])
  @@index([updated_at])
}

model payload_preferences {
  id                       Int                        @id @default(autoincrement())
  key                      String?                    @db.VarChar
  value                    Json?
  updated_at               DateTime                   @default(now()) @db.Timestamptz(3)
  created_at               DateTime                   @default(now()) @db.Timestamptz(3)
  payload_preferences_rels payload_preferences_rels[]

  @@index([created_at])
  @@index([key])
  @@index([updated_at])
}

model payload_preferences_rels {
  id                  Int                 @id @default(autoincrement())
  order               Int?
  parent_id           Int
  path                String              @db.VarChar
  users_id            Int?
  payload_preferences payload_preferences @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_parent_fk")
  users               User?               @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_users_fk")

  @@index([order])
  @@index([parent_id], map: "payload_preferences_rels_parent_idx")
  @@index([path])
  @@index([users_id])
}

model Source {
  id                            Int                             @id @default(autoincrement())
  title                         String                          @db.VarChar
  site                          String                          @db.VarChar
  rss_url                       String                          @db.VarChar
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  articles                      Article[]
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([created_at])
  @@index([updated_at])
  @@map("sources")
}

model User {
  id                            Int                             @id @default(autoincrement())
  updated_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  email                         String                          @unique(map: "users_email_idx") @db.VarChar
  reset_password_token          String?                         @db.VarChar
  reset_password_expiration     DateTime?                       @db.Timestamptz(3)
  salt                          String?                         @db.VarChar
  hash                          String?                         @db.VarChar
  login_attempts                Decimal?                        @default(0) @db.Decimal
  lock_until                    DateTime?                       @db.Timestamptz(3)
  bookmarks                     Bookmark[]
  payload_locked_documents_rels payload_locked_documents_rels[]
  payload_preferences_rels      payload_preferences_rels[]
  users_sessions                users_sessions[]
  vote_records                  Vote[]

  @@index([created_at])
  @@index([updated_at])
  @@map("users")
}

model users_sessions {
  order      Int       @map("_order")
  parent_id  Int       @map("_parent_id")
  id         String    @id @db.VarChar
  created_at DateTime? @db.Timestamptz(3)
  expires_at DateTime  @db.Timestamptz(3)
  users      User      @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "users_sessions_parent_id_fk")

  @@index([order], map: "users_sessions_order_idx")
  @@index([parent_id], map: "users_sessions_parent_id_idx")
}

model Vote {
  id        String   @id
  articleId Int
  ipAddress String?
  createdAt DateTime @default(now())
  userId    Int?
  article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([ipAddress, articleId])
  @@unique([userId, articleId])
  @@index([articleId])
  @@index([createdAt])
  @@index([userId])
  @@map("votes")
}

enum enum_articles_post_type {
  ARTICLE
  VIDEOYT
  VIDEOOTHER
}

enum enum_articles_status {
  draft
  published
  archived
}
