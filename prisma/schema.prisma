generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Article {
  id                   Int        @id @default(autoincrement())
  title                String
  post_type            PostType   @default(ARTICLE)
  content              String
  summary              String?
  summarization_vendor String?
  slug                 String
  url                  String     @unique
  author               String?
  source_id            Int
  tags                 String[]
  published_date       DateTime
  thumbnail_key        String?
  views                Int?       @default(0)
  votes                Int?       @default(0)
  video_id             String?
  source               Source     @relation(fields: [source_id], references: [id])
  bookmarks            Bookmark[]
  userVotes            Vote[]

  @@index([source_id])
  @@index([published_date])
  @@index([votes])
  @@map("articles")
}

model Source {
  id       Int       @id @default(autoincrement())
  title    String
  site     String
  rss_url  String
  articles Article[]

  @@map("sources")
}

model User {
  id                            Int                             @id @default(autoincrement())
  updated_at                    DateTime                        @default(now()) @updatedAt @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  email                         String                          @unique(map: "users_email_idx") @db.VarChar
  reset_password_token          String?                         @db.VarChar
  reset_password_expiration     DateTime?                       @db.Timestamptz(3)
  salt                          String?                         @db.VarChar
  hash                          String?                         @db.VarChar
  login_attempts                Decimal?                        @default(0) @db.Decimal
  lock_until                    DateTime?                       @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]
  payload_preferences_rels      payload_preferences_rels[]

  @@index([updated_at])
  @@index([created_at])
  @@map("users")
}

model Vote {
  id        String   @id @default(cuid())
  articleId Int
  ipAddress String?
  createdAt DateTime @default(now())
  userId    Int?
  article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@unique([ipAddress, articleId])
  @@map("votes")
}

model Bookmark {
  id        String   @id @default(cuid())
  articleId Int
  createdAt DateTime @default(now())
  userId    Int
  article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@map("bookmarks")
}

model PayloadMigration {
  id         Int      @id @default(autoincrement())
  name       String?  @db.VarChar
  batch      Decimal? @db.Decimal
  updated_at DateTime @default(now()) @updatedAt @db.Timestamptz(3)
  created_at DateTime @default(now()) @db.Timestamptz(3)

  @@index([updated_at])
  @@index([created_at])
  @@map("payload_migrations")
}

model PayloadPreference {
  id                       Int                        @id @default(autoincrement())
  key                      String?                    @db.VarChar
  value                    Json?
  updated_at               DateTime                   @default(now()) @updatedAt @db.Timestamptz(3)
  created_at               DateTime                   @default(now()) @db.Timestamptz(3)
  payload_preferences_rels payload_preferences_rels[]

  @@index([key])
  @@index([updated_at])
  @@index([created_at])
  @@map("payload_preferences")
}

model PayloadLockedDocument {
  id                            Int                             @id @default(autoincrement())
  global_slug                   String?                         @db.VarChar
  updated_at                    DateTime                        @default(now()) @updatedAt @db.Timestamptz(3)
  created_at                    DateTime                        @default(now()) @db.Timestamptz(3)
  payload_locked_documents_rels payload_locked_documents_rels[]

  @@index([global_slug])
  @@index([updated_at])
  @@index([created_at])
  @@map("payload_locked_documents")
}

model payload_locked_documents_rels {
  id                       Int                   @id @default(autoincrement())
  order                    Int?
  parent_id                Int
  path                     String                @db.VarChar
  users_id                 Int?
  payload_locked_documents PayloadLockedDocument @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_parent_fk")
  users                    User?                 @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_locked_documents_rels_users_fk")

  @@index([order])
  @@index([parent_id], map: "payload_locked_documents_rels_parent_idx")
  @@index([path])
  @@index([users_id])
}

model payload_preferences_rels {
  id                  Int               @id @default(autoincrement())
  order               Int?
  parent_id           Int
  path                String            @db.VarChar
  users_id            Int?
  payload_preferences PayloadPreference @relation(fields: [parent_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_parent_fk")
  users               User?             @relation(fields: [users_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "payload_preferences_rels_users_fk")

  @@index([order])
  @@index([parent_id], map: "payload_preferences_rels_parent_idx")
  @@index([path])
  @@index([users_id])
}

enum PostType {
  ARTICLE
  VIDEOYT
  VIDEOOTHER
}
