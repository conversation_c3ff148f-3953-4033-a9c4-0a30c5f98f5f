-- Rename tables from PascalCase to lowercase

-- Rename Article table to articles
ALTER TABLE "Article" RENAME TO "articles";

-- Rename Source table to sources  
ALTER TABLE "Source" RENAME TO "sources";

-- Rename User table to users
ALTER TABLE "User" RENAME TO "users";

-- Update foreign key constraint names to match new table names
-- Note: PostgreSQL automatically updates constraint names when tables are renamed,
-- but we need to ensure the foreign key references are correct

-- Update any indexes that reference the old table names (if needed)
-- Most indexes should be automatically updated by PostgreSQL

-- Update any sequences that might reference the old table names
-- PostgreSQL should handle this automatically for SERIAL columns
