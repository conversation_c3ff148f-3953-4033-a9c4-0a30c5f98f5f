import { PrismaClient } from '@prisma/client';

async function fixContentColumn() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Fixing content column type...');
    
    // First, let's see what's in the articles table
    const articles = await prisma.$queryRaw`SELECT id, title, content FROM articles LIMIT 5`;
    console.log('Sample articles:', articles);
    
    // Update the content column to be compatible with jsonb
    // If content is currently text, we need to convert it to a proper JSON structure
    await prisma.$executeRaw`
      UPDATE articles 
      SET content = CASE 
        WHEN content IS NULL THEN NULL
        WHEN content = '' THEN NULL
        ELSE json_build_object('root', json_build_object('children', json_build_array(json_build_object('children', json_build_array(json_build_object('detail', 0, 'format', 0, 'mode', 'normal', 'style', '', 'text', content, 'type', 'text', 'version', 1)), 'direction', 'ltr', 'format', '', 'indent', 0, 'type', 'paragraph', 'version', 1)), 'direction', 'ltr', 'format', '', 'indent', 0, 'type', 'root', 'version', 1))
      END::jsonb
    `;
    
    console.log('Content column updated successfully!');
    
    // Now alter the column type
    await prisma.$executeRaw`ALTER TABLE articles ALTER COLUMN content TYPE jsonb USING content::jsonb`;
    
    console.log('Column type changed to jsonb successfully!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixContentColumn();
