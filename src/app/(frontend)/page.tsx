'use client';

// Removed unused imports - now handled by ArticleCard component
import { useState, useEffect, useRef } from 'react';
import { useRouter } from "next/navigation";
import Link from 'next/link';
import ArticleSkeleton from '../../components/ArticleSkeleton';
import TagFilterBar from '@/components/TagFilterBar';
import ArticleCard from '@/components/ArticleCard';
import { Article } from '@/app/(frontend)/utils/article'


import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Badge} from "@/components/ui/badge";
import {useRedirect} from "@/hooks/useRedirect";

export default function Home() {
  const [activeFilter, setActiveFilter] = useState('Recent');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [articles, setArticles] = useState<Article[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
    const router = useRouter();

  const fetchArticles = async (pageNum: number) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '15',
        filter: activeFilter
      });

      // Add tags if any are selected
      if (selectedTags.length > 0) {
        params.set('tags', selectedTags.join(','));
      }

      const response = await fetch(`/api/articles?${params.toString()}`);
      const data = await response.json();

      setArticles(prev => pageNum === 1 ? data.articles : [...prev, ...data.articles]);
      setHasMore(data.pagination.hasMore);

    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
    }
  };


  const handleClickAction = (article: Article) => {
    // router.push('article/' + article.slug)
    if (window.innerWidth <= 768) {
        router.push('article/' + article.slug);
        return;
    }

    window.history.pushState({}, "", window.location.protocol + '//' + window.location.host + '/article/'+ article.slug )
    setSelectedArticle(article);
  }

  useEffect(() => {
    setPage(1);
    fetchArticles(1);
  }, [activeFilter, selectedTags]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage(prev => prev + 1);
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading]);

  useEffect(() => {
    if (page > 1) {
      fetchArticles(page);
    }
  }, [page]);


  return (
    <>
      {/* Tag Filter Bar */}
      <TagFilterBar
        activeFilter={activeFilter}
        setActiveFilter={setActiveFilter}
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        showThumbnails={showThumbnails}
        setShowThumbnails={setShowThumbnails}
      />

      <div className="max-w-[2000px] mx-auto p-6">
        {/* News Grid - Updated to 5 columns on desktop */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {articles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            showThumbnails={showThumbnails}
            onClick={handleClickAction}
          />
        ))}

        {/* Loading Skeletons */}
        {loading && (
          <>
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
          </>
        )}
      </div>

      {/* Intersection Observer Target */}
      <div ref={observerTarget} className="h-4 mt-8" />

      {/* End of Content Message */}
      {!hasMore && !loading && articles.length > 0 && (
        <p className="text-center text-gray-500 dark:text-gray-400 mt-8">
          No more articles to load
        </p>
      )}

      <Dialog open={!!selectedArticle} onOpenChange={(open) => !open && setSelectedArticle(null)}>
        <DialogContent className="sm:max-w-[725px]">
          <DialogHeader>

            <Link href={ useRedirect(selectedArticle?.slug) ?? ''} target="_blank">
            <div className="flex items-center aspect-video relative mb-4">
              <div className="bg-gray-100 p-2 rounded-lg">
                <img
                    src={selectedArticle?.thumbnail_key}
                    alt={selectedArticle?.title}
                    //  fill
                    className="object-cover rounded-lg"
                />
              </div>
            </div>

            </Link>

            <DialogTitle>{selectedArticle?.title}</DialogTitle>
          </DialogHeader>
          <div className="mt-4 text-sm text-gray-600">
            <p>
              <span className="font-semibold">TLDR</span>
              <br />
              {selectedArticle?.summary}
            </p>
            <div className="mt-2">
              <Badge variant="secondary" className="text-gray-500">{selectedArticle?.category}</Badge>
            </div>
          </div>
          <div className="mt-4 prose">

          </div>
        </DialogContent>
      </Dialog>


    </div>
    </>
  );
}
