@import "tailwindcss";


body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    /* Brand Colors */
    --electric-blue: 15 98% 54%; /* #0F62FE */
    --vibrant-purple: 252 77% 57%; /* #7C3AED */

    /* Light Mode Colors */
    --background: 249 250% 98%; /* #F9FAFB */
    --foreground: 17 24% 9%; /* #111827 */
    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 17 24% 9%; /* #111827 */
    --popover: 0 0% 100%;
    --popover-foreground: 17 24% 9%;
    --primary: 15 98% 54%; /* Electric Blue */
    --primary-foreground: 0 0% 98%;
    --secondary: 252 77% 57%; /* Vibrant Purple */
    --secondary-foreground: 0 0% 98%;
    --muted: 220 13% 91%; /* #E5E7EB */
    --muted-foreground: 220 9% 46%; /* #6B7280 */
    --accent: 252 77% 57%; /* Vibrant Purple */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%; /* #EF4444 */
    --destructive-foreground: 0 0% 98%;
    --success: 142 71% 45%; /* #10B981 */
    --warning: 38 92% 50%; /* #F59E0B */
    --border: 220 13% 91%; /* #E5E7EB */
    --input: 220 13% 91%;
    --ring: 15 98% 54%; /* Electric Blue */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark Mode Colors */
    --background: 0 0% 7%; /* #121212 */
    --foreground: 210 40% 98%; /* #F3F4F6 */
    --card: 0 0% 12%; /* #1F1F1F */
    --card-foreground: 210 40% 98%; /* #F3F4F6 */
    --popover: 0 0% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 15 98% 54%; /* Electric Blue - same in dark */
    --primary-foreground: 0 0% 98%;
    --secondary: 252 77% 57%; /* Vibrant Purple - same in dark */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%; /* #262626 */
    --muted-foreground: 220 9% 64%; /* #9CA3AF */
    --accent: 252 77% 57%; /* Vibrant Purple */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%; /* #EF4444 */
    --destructive-foreground: 0 0% 98%;
    --success: 142 71% 45%; /* #10B981 */
    --warning: 38 92% 50%; /* #F59E0B */
    --border: 0 0% 20%; /* #333333 */
    --input: 0 0% 20%;
    --ring: 15 98% 54%; /* Electric Blue */
  }
}

/*@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}*/

/* Custom gradient animations for article cards without thumbnails */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-fallback {
  background: linear-gradient(-45deg, hsl(var(--electric-blue)), hsl(var(--vibrant-purple)), hsl(var(--electric-blue)), hsl(var(--vibrant-purple)));
  background-size: 400% 400%;
  animation: gradientShift 8s ease-in-out infinite;
}

/* Tag button styles */
.tag-button {
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid #d1d5db;
  color: #374151;
}

.dark .tag-button {
  border-color: #4b5563;
  color: #d1d5db;
}

.tag-button:hover {
  border-color: hsl(var(--electric-blue));
  color: hsl(var(--electric-blue));
}

.tag-button.active {
  background: hsl(var(--electric-blue));
  border-color: hsl(var(--electric-blue));
  color: white;
}

/* Filter button styles */
.filter-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid #d1d5db;
  color: #374151;
}

.dark .filter-button {
  border-color: #4b5563;
  color: #d1d5db;
}

.filter-button:hover {
  border-color: hsl(var(--electric-blue));
  color: hsl(var(--electric-blue));
}

.filter-button.active {
  background: hsl(var(--electric-blue));
  border-color: hsl(var(--electric-blue));
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Prevent sidebar flash on mobile by hiding it by default */
@layer utilities {
  .sidebar-mobile-hidden {
    transform: translateX(-100%);
  }

  @media (min-width: 768px) {
    .sidebar-mobile-hidden {
      transform: translateX(0);
    }
  }
}
