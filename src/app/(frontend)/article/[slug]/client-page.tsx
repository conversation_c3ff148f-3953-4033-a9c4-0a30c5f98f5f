// app/articles/[slug]/client-page.js (Client Component)
'use client';

import { ArrowLeft, Heart, MessageCircle, Share2, Clock, Globe, Copy, Check } from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect} from 'react';
import { Article } from '@/app/(frontend)/utils/article';

export default function ClientArticlePage({ initialArticle, slug }) {
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/articles/by-slug/${slug}`);
        if (!response.ok) throw new Error('Article not found');
        const data = await response.json();
        setArticle(data);



      } catch (error) {
        console.error('Error fetching article:', error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [slug]);

  const handleCopyLink = async () => {
    try {
      const url = `${window.location.origin}/article/${slug}`;
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6 animate-pulse">
        <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-8" />
        <div className="space-y-4">
          <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded-full" />
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-6 border-y border-gray-200 dark:border-gray-700">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20" />
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24" />
              </div>
            ))}
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-500">Article not found</h1>
        <Link 
          href="/"
          className="mt-4 inline-flex items-center text-blue-500 hover:text-blue-600"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to home
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Link 
        href="/"
        className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 mb-6"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to home
      </Link>

      {/* Article Header */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
            {article.category}
          </span>
        </div>

        <div className="flex items-start justify-between gap-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex-1">
            {article.title}
          </h1>

          {/* Copy Link Button */}
          <button
            onClick={handleCopyLink}
            className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors text-sm font-medium text-gray-700 dark:text-gray-300"
            title="Copy article link"
          >
            {copySuccess ? (
              <>
                <Check className="w-4 h-4" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="w-4 h-4" />
                Share
              </>
            )}
          </button>
        </div>

        {/* Metadata Grid */}
        <div className="grid grid-cols-2 md:grid-cols-2 gap-4 py-6 border-y border-gray-200 dark:border-gray-700">
          <div className="space-y-1">
            <p className="text-sm text-gray-500 dark:text-gray-400">Source</p>
            <div className="flex items-center text-gray-900 dark:text-white">
              <Globe className="w-4 h-4 mr-2" />
              {article.source?.title}
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-gray-500 dark:text-gray-400">Published</p>
            <div className="flex items-center text-gray-900 dark:text-white">
              <Clock className="w-4 h-4 mr-2" />
              {article.timestamp}
            </div>
          </div>
        </div>

            {/*
          <div className="space-y-1">
            <p className="text-sm text-gray-500 dark:text-gray-400">Likes</p>
            <div className="flex items-center text-gray-900 dark:text-white">
              <Heart className="w-4 h-4 mr-2" />
              {article.likes}
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-gray-500 dark:text-gray-400">Comments</p>
            <div className="flex items-center text-gray-900 dark:text-white">
              <MessageCircle className="w-4 h-4 mr-2" />
              {article.comments}
            </div>
          </div>
        </div>
      */}

         {/*Summary */}
        <p className="text-gray-600 dark:text-gray-300 text-lg">
          {article.summary}
        </p>

        <button className="mt-6 inline-flex items-center px-4 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
          <Link href={"https://tech-news.io/r/" + article?.slug} target='_blank'>
             Read Full Article
          </Link>
         
        </button>
      </div>
    </div>
  );
}
