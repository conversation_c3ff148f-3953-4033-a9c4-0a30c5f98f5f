import { ArrowLeft, Heart, MessageCircle, Share2, Clock, Globe } from 'lucide-react';
import Link from 'next/link';
// import { useParams } from 'next/navigation';
import { Article, getArticleBySlug } from '../../utils/article'
import { Metadata } from 'next';
import ClientArticlePage from './client-page';


// Cache for article data to avoid duplicate fetches within the same request
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const requestCache = new Map<string, any>();

async function getCachedArticle(slug: string) {
  // Use a request-scoped cache key
  const cacheKey = `article-${slug}`;

  if (requestCache.has(cacheKey)) {
    console.log(`[CACHE HIT] Article ${slug} served from cache`);
    return requestCache.get(cacheKey);
  }

  console.log(`[DB QUERY START] Fetching article ${slug} from database...`);
  const startTime = Date.now();

  const article = await getArticleBySlug(slug);

  const endTime = Date.now();
  console.log(`[DB QUERY END] Article ${slug} fetched in ${endTime - startTime}ms`);

  requestCache.set(cacheKey, article);

  // Clear cache after request (Next.js will handle this automatically)
  // but we'll also set a timeout as backup
  setTimeout(() => {
    requestCache.delete(cacheKey);
  }, 30 * 1000); // 30 seconds

  return article;
}

export async function generateMetadata({ params }) : Promise<Metadata> {
  // This runs on the server and is SEO-friendly
  const { slug } = await params;

  const article = await getCachedArticle(slug);

  return {
    title: `${article.title} - Tech News Aggregator`,
    description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
    openGraph: {
      title: `${article.title} - Tech News Aggregator`,
      description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
      images: [{ url: "/og-image.jpg"}] ,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
      images: article.thumbnail_key ? [article.thumbnail_key] : ['/twitter-card.jpg'],
    },
  };
}


export default async function ArticlePage({ params }) {
 //  const params = useParams();
  const { slug } = await params;

  // Use cached article to avoid duplicate database call
  const article = await getCachedArticle(slug);

  // Pass the pre-fetched article to the client component
  return <ClientArticlePage initialArticle={article} slug={slug} />;
}