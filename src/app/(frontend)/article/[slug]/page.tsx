import { ArrowLeft, Heart, MessageCircle, Share2, Clock, Globe } from 'lucide-react';
import Link from 'next/link';
// import { useParams } from 'next/navigation';
import { Article, getArticleBySlug } from '../../utils/article'
import { Metadata } from 'next';
import ClientArticlePage from './client-page';


// Cache for article data to avoid duplicate fetches
const articleCache = new Map<string, any>();

async function getCachedArticle(slug: string) {
  if (articleCache.has(slug)) {
    return articleCache.get(slug);
  }

  const article = await getArticleBySlug(slug);
  articleCache.set(slug, article);

  // Clear cache after 5 minutes to prevent memory leaks
  setTimeout(() => {
    articleCache.delete(slug);
  }, 5 * 60 * 1000);

  return article;
}

export async function generateMetadata({ params }) : Promise<Metadata> {
  // This runs on the server and is SEO-friendly
  const { slug } = await params;

  const article = await getCachedArticle(slug);

  return {
    title: `${article.title} - Tech News Aggregator`,
    description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
    openGraph: {
      title: `${article.title} - Tech News Aggregator`,
      description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
      images: [{ url: "/og-image.jpg"}] ,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
      images: article.thumbnail_key ? [article.thumbnail_key] : ['/twitter-card.jpg'],
    },
  };
}


export default async function ArticlePage({ params }) {
 //  const params = useParams();
  const { slug } = await params;

  // Use cached article to avoid duplicate database call
  const article = await getCachedArticle(slug);

  // Pass the pre-fetched article to the client component
  return <ClientArticlePage initialArticle={article} slug={slug} />;
}