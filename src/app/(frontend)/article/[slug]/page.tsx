import { ArrowLeft, Heart, MessageCircle, Share2, Clock, Globe } from 'lucide-react';
import Link from 'next/link';
// import { useParams } from 'next/navigation';
import { Article, getArticleBySlug } from '../../utils/article'
import { Metadata } from 'next';
import ClientArticlePage from './client-page';


// Cache for article data to avoid duplicate fetches within the same request
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const requestCache = new Map<string, any>();

async function getCachedArticle(slug: string) {
  // Use a request-scoped cache key
  const cacheKey = `article-${slug}`;

  if (requestCache.has(cacheKey)) {
    console.log(`[CACHE HIT] Article ${slug} served from cache`);
    return requestCache.get(cacheKey);
  }

  console.log(`[DB QUERY START] Fetching article ${slug} from database...`);
  const startTime = Date.now();

  const article = await getArticleBySlug(slug);

  const endTime = Date.now();
  console.log(`[DB QUERY END] Article ${slug} fetched in ${endTime - startTime}ms`);

  requestCache.set(cacheKey, article);

  // Clear cache after request (Next.js will handle this automatically)
  // but we'll also set a timeout as backup
  setTimeout(() => {
    requestCache.delete(cacheKey);
  }, 30 * 1000); // 30 seconds

  return article;
}

export async function generateMetadata({ params }) : Promise<Metadata> {
  try {
    const { slug } = await params;

    // Use a shorter timeout for metadata to prevent blocking page loads
    const article = await Promise.race([
      getCachedArticle(slug),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Metadata timeout')), 2000) // Reduced to 2 seconds
      )
    ]);

    if (!article) {
      throw new Error('Article not found');
    }

    return {
      metadataBase: new URL(process.env.NODE_ENV === 'production' ? 'https://tech-news.io' : 'http://localhost:3001'),
      title: `${article.title} - Tech News Aggregator`,
      description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
      openGraph: {
        title: `${article.title} - Tech News Aggregator`,
        description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
        images: [{ url: "/og-image.jpg"}] ,
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description: article.summary?.substring(0, 160) || 'Read the latest technology news and updates.',
        images: article.thumbnail_key ? [article.thumbnail_key] : ['/twitter-card.jpg'],
      },
    };
  } catch (error) {
    console.log('[METADATA FALLBACK] Using fallback metadata due to:', error.message);

    // Extract slug for better fallback title
    const { slug } = await params;
    const fallbackTitle = slug ?
      `${slug.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())} - Tech News Aggregator` :
      'Tech News Article - Tech News Aggregator';

    // Return fallback metadata if database is slow
    return {
      metadataBase: new URL(process.env.NODE_ENV === 'production' ? 'https://tech-news.io' : 'http://localhost:3001'),
      title: fallbackTitle,
      description: 'Read the latest technology news and updates from top tech sources.',
      openGraph: {
        title: fallbackTitle,
        description: 'Read the latest technology news and updates from top tech sources.',
        images: [{ url: "/og-image.jpg"}] ,
      },
      twitter: {
        card: 'summary_large_image',
        title: fallbackTitle,
        description: 'Read the latest technology news and updates from top tech sources.',
        images: ['/twitter-card.jpg'],
      },
    };
  }
}


export default async function ArticlePage({ params }) {
  const { slug } = await params;

  try {
    // Set a shorter timeout for server-side rendering to prevent long delays
    const article = await Promise.race([
      getCachedArticle(slug),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Server timeout')), 3000) // Reduced to 3 seconds
      )
    ]);

    console.log('[SERVER SUCCESS] Article loaded server-side');
    // Pass the pre-fetched article to the client component
    return <ClientArticlePage initialArticle={article} slug={slug} />;

  } catch (error) {
    console.log('[SERVER FALLBACK] Using client-side loading due to:', error.message);
    // Fallback to client-side loading if server is too slow
    return <ClientArticlePage initialArticle={null} slug={slug} />;
  }
}