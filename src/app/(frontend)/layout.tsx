import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '../../contexts/AuthContext';
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from '@vercel/speed-insights/next'
import { GoogleAnalytics } from '@next/third-parties/google'
import CookieConsent from "@/components/CookieConsent";
import CalltoActionBanner from "@/components/CalltoActionBanner";
import ClientLayout from "@/components/ClientLayout";


const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NODE_ENV === 'production' ? 'https://tech-news.io' : 'http://localhost:3001'),
  title: 'Tech News Aggregator - Latest Technology News & Updates',
  description: 'Stay updated with the latest technology news from top sources, Real-time updates and personalized recommendations.',
  openGraph: {
    title: 'Tech News Aggregator - Latest Technology News & Updates',
    description: 'Your one-stop destination for the latest tech news, featuring curated content from leading technology publications. Get real-time updates and personalized recommendations.',
    url: 'https://tech-news.io',
    images: [
      {
        url: '/og-image.jpg',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tech News Aggregator - Latest Tech Updates',
    description: 'Stay informed with real-time tech news from TechCrunch, The Verge, Wired, and more. Personalized feed and instant updates.',
    images: ['/twitter-card.jpg'],
  },
}



/*
* <div className="flex">
          <Sidebar  isOpen={sidebarOpen}
                    setIsOpen={setSidebarOpen} />
          <div className={`flex-1 ${isMobile ? 'ml-0' : (sidebarOpen ? 'ml-64' : 'ml-0')} transition-all duration-300`}>
            <header
                className="h-16 fixed top-0 right-0 left-64 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 z-10">
              <div className="flex items-center justify-between h-full px-6">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tech News Feed</h1>
                <div className="flex items-center space-x-4">
                  <ThemeToggle/>
                  <UserMenu/>
                </div>
              </div>
            </header>
            <main className="pt-16">
              {children}
            </main>
          </div>
        </div>
*
* */

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
      <html lang="en" suppressHydrationWarning>
        <body className={`${inter.className} bg-gray-50 dark:bg-gray-900`}>
          <script
            dangerouslySetInnerHTML={{
              __html: `(()=>{try{const s=localStorage.getItem('theme');const m=window.matchMedia&&window.matchMedia('(prefers-color-scheme: dark)').matches;const d=s? s==='dark': m;const r=document.documentElement.classList;d?r.add('dark'):r.remove('dark')}catch(e){}})();`
            }}
          />
          <GoogleAnalytics gaId="G-H8D2RF7D70"></GoogleAnalytics>
          <CookieConsent/>
          <Analytics/>
          <SpeedInsights/>
          <CalltoActionBanner />
          <AuthProvider>
              <ClientLayout>
                  {children}
              </ClientLayout>
          </AuthProvider>
        </body>
      </html>
  );
}
