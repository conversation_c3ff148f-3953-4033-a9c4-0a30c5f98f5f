import { NextResponse } from 'next/server';
import { prisma } from '@/db/prisma';

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const articleId = parseInt(id);
    
    if (isNaN(articleId)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 });
    }

    // Get IP address for anonymous voting
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    // Check if this IP has already voted for this article
    const existingVote = await prisma.vote.findUnique({
      where: {
        ipAddress_articleId: {
          ipAddress,
          articleId
        }
      }
    });

    if (existingVote) {
      return NextResponse.json({ error: 'Already voted' }, { status: 409 });
    }

    // Create the vote and update article vote count in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the vote
      await tx.vote.create({
        data: {
          article: { connect: { id: articleId } },
          ipAddress,
          // Anonymous vote: omit user entirely
        }
      });

      // Update the article vote count
      const updatedArticle = await tx.article.update({
        where: { id: articleId },
        data: {
          votes: {
            increment: 1
          }
        },
        select: {
          id: true,
          votes: true
        }
      });

      return updatedArticle;
    });

    return NextResponse.json({ 
      success: true, 
      votes: result.votes 
    });

  } catch (error) {
    console.error('Error voting on article:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const articleId = parseInt(id);
    
    if (isNaN(articleId)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 });
    }

    // Get IP address for anonymous voting
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    // Check if this IP has voted for this article
    const existingVote = await prisma.vote.findUnique({
      where: {
        ipAddress_articleId: {
          ipAddress,
          articleId
        }
      }
    });

    if (!existingVote) {
      return NextResponse.json({ error: 'Vote not found' }, { status: 404 });
    }

    // Remove the vote and update article vote count in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Delete the vote
      await tx.vote.delete({
        where: {
          id: existingVote.id
        }
      });

      // Update the article vote count
      const updatedArticle = await tx.article.update({
        where: { id: articleId },
        data: {
          votes: {
            decrement: 1
          }
        },
        select: {
          id: true,
          votes: true
        }
      });

      return updatedArticle;
    });

    return NextResponse.json({ 
      success: true, 
      votes: result.votes 
    });

  } catch (error) {
    console.error('Error removing vote:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// GET endpoint to check if user has voted
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const articleId = parseInt(id);
    
    if (isNaN(articleId)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 });
    }

    // Get IP address for anonymous voting
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    // Check if this IP has voted for this article
    const existingVote = await prisma.vote.findUnique({
      where: {
        ipAddress_articleId: {
          ipAddress,
          articleId
        }
      }
    });

    return NextResponse.json({ 
      hasVoted: !!existingVote 
    });

  } catch (error) {
    console.error('Error checking vote status:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
