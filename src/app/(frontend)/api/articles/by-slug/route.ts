import { NextResponse } from 'next/server';
import {getArticleBySlug} from "@/app/(frontend)/utils/article";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    console.log('[API] Starting article fetch...');
    const startTime = Date.now();

    const { slug } = await params;

    // Add timeout to API route as well
    const article = await Promise.race([
      getArticleBySlug(slug),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('API timeout')), 8000)
      )
    ]);

    const endTime = Date.now();
    console.log(`[API] Article fetched in ${endTime - startTime}ms`);

    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    return NextResponse.json(article);
  } catch (error) {
    console.error('[API ERROR]', error);
    return NextResponse.json(
      { error: 'Failed to fetch article' },
      { status: 500 }
    );
  }
}
