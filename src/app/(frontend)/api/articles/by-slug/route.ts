import { NextResponse } from 'next/server';
import {getArticleBySlug} from "@/app/(frontend)/utils/article";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  // Simulate server delay
  await new Promise(resolve => setTimeout(resolve, 500));

    const { slug } = await params
    const article = await getArticleBySlug(slug);
    return NextResponse.json(article);
}
