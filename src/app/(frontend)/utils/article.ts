import {prisma} from '@/db/prisma'
import {Prisma} from '@prisma/client'

export interface Article {
    id: string;
    slug: string;
    url: string;
    category: string;
    title: string;
    content: string;
    summary?: string;
    summarization_vendor?: string;
    source_id: number;
    source: { title: string};
    timestamp: string;
    likes: number;
    comments: number;
    thumbnail_key?: string;
}

const POSTS_PER_PAGE = 50;

async function retryDatabaseQuery<T>(
    queryFn: () => Promise<T>,
    maxRetries: number = 2, // Reduced retries for faster fallback
    delay: number = 500     // Reduced initial delay
): Promise<T | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`[DB ATTEMPT ${attempt}] Starting database query...`);
            const startTime = Date.now();

            // Add timeout to individual query attempts
            const result = await Promise.race([
                queryFn(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Query timeout')), 2000)
                )
            ]);

            const endTime = Date.now();
            console.log(`[DB SUCCESS] Query completed in ${endTime - startTime}ms on attempt ${attempt}`);

            return result as T;
        } catch (error) {
            console.error(`[DB ERROR] Attempt ${attempt} failed:`, error);

            if (attempt === maxRetries) {
                console.error(`[DB FAILED] All ${maxRetries} attempts failed`);
                return null;
            }

            // Wait before retrying with shorter delay
            console.log(`[DB RETRY] Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            delay *= 1.5; // Smaller exponential backoff
        }
    }
    return null;
}

export async function getArticleBySlug(slug: string) {
    console.log(`[ARTICLE FETCH] Starting fetch for slug: ${slug}`);

    return await retryDatabaseQuery(async () => {
        return await prisma.article.findFirst({
            where: {
                slug: slug
            },
            include: {
                source: {
                    select: { title: true}
                }
            }
        });
    });
}

export async function getLatestArticles(page : number = 1, filter?: string, source_id?: string, selectedTags?: string[]) {
    try {
        // Build where clause
        const whereClause: Prisma.ArticleWhereInput = {};

        // Add source filter if provided
        if (source_id) {
            whereClause.source_id = parseInt(source_id);
        }

        // Add tag filtering if tags are selected (OR logic)
        if (selectedTags && selectedTags.length > 0) {
            whereClause.tags = {
                hasSome: selectedTags
            };
        }

        // Determine sort order based on filter
        let orderBy: Prisma.ArticleOrderByWithRelationInput[] = [];
        if (filter === 'Popular') {
            orderBy = [
                { votes: 'desc' },
                { views: 'desc' },
                { published_date: 'desc' }
            ];
        } else if (filter === 'Trending') {
            // Trending: high votes in recent time
            orderBy = [
                { votes: 'desc' },
                { published_date: 'desc' }
            ];
        } else {
            // Recent (default)
            orderBy = [
                { published_date: 'desc' }
            ];
        }

        const articles = await prisma.article.findMany({
            where: whereClause,
            take: POSTS_PER_PAGE,
            orderBy,
            skip: (page - 1) * POSTS_PER_PAGE,
            include: {
                source: {
                    select: {
                        title: true
                    }
                }
            }
        })

       //  const articles = []
        const totalCount = await prisma.article.count({
             where: source_id ? { source_id: parseInt(source_id) } : undefined
        })

        return {
            articles,
            pagination: {
                page,
                total: Math.ceil(totalCount / POSTS_PER_PAGE),
                hasMore: page * POSTS_PER_PAGE < totalCount
            }
        }

    } catch (error) {
        console.error('Error in getLatestArticles:', error);
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            console.log('Prisma error code:', error.code);
            if (error.code === 'P101') {
                console.log('Can\'t reach database server');
            }
        }

        // Return a properly structured error response
        return {
            articles: [],
            pagination: {
                page: 1,
                total: 0,
                hasMore: false
            },
            error: 'Failed to fetch articles'
        };
    }
}

export async function incrViews(post_id: number) {
    try {
        return await prisma.article.update({
            where: { id: post_id },
            data: {views: {increment: 1}}
        })
    }
    catch(error) {
        console.error('Error', error)
        return null;
    }
}
