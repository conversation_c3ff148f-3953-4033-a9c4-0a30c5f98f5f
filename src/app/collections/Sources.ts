import type { CollectionConfig } from 'payload'

export const Sources: CollectionConfig = {
  slug: 'sources',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'url', 'rss_url', 'thumbnail_css_path'],
    group: 'Content',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The name of the news source (e.g., TechCrunch)',
      },
    },
    {
      name: 'url',
      type: 'text',
      required: true,
      admin: {
        description: 'The website URL (e.g., https://techcrunch.com)',
      },
    },
    {
      name: 'rss_url',
      type: 'text',
      required: true,
      admin: {
        description: 'RSS feed URL for this source',
      },
    },
    {
      name: 'thumbnail_css_path',
      type: 'text',
      required: false,
      admin: {
        description: 'CSS selector used to extract a thumbnail image from the source site (optional)'
      }
    },
  ],
}
