"use client";

import { useState, useEffect } from 'react';

const BOOKMARKS_KEY = 'tech-news-bookmarks';

export interface BookmarkedArticle {
  id: string;
  title: string;
  slug: string;
  bookmarkedAt: string;
}

export function useBookmarks() {
  const [bookmarks, setBookmarks] = useState<BookmarkedArticle[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load bookmarks from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(BOOKMARKS_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setBookmarks(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading bookmarks:', error);
      setBookmarks([]);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save bookmarks to localStorage whenever bookmarks change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(BOOKMARKS_KEY, JSON.stringify(bookmarks));
      } catch (error) {
        console.error('Error saving bookmarks:', error);
      }
    }
  }, [bookmarks, isLoaded]);

  const isBookmarked = (articleId: string): boolean => {
    return bookmarks.some(bookmark => bookmark.id === articleId);
  };

  const addBookmark = (article: { id: string; title: string; slug: string }) => {
    if (!isBookmarked(article.id)) {
      const newBookmark: BookmarkedArticle = {
        id: article.id,
        title: article.title,
        slug: article.slug,
        bookmarkedAt: new Date().toISOString()
      };
      setBookmarks(prev => [newBookmark, ...prev]);
    }
  };

  const removeBookmark = (articleId: string) => {
    setBookmarks(prev => prev.filter(bookmark => bookmark.id !== articleId));
  };

  const toggleBookmark = (article: { id: string; title: string; slug: string }) => {
    if (isBookmarked(article.id)) {
      removeBookmark(article.id);
    } else {
      addBookmark(article);
    }
  };

  const clearAllBookmarks = () => {
    setBookmarks([]);
  };

  return {
    bookmarks,
    isLoaded,
    isBookmarked,
    addBookmark,
    removeBookmark,
    toggleBookmark,
    clearAllBookmarks,
    bookmarkCount: bookmarks.length
  };
}
