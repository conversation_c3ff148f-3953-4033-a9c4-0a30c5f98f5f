"use client";

import { useState, useEffect } from 'react';

export function useVoting(articleId: string, initialVotes: number = 0) {
  const [votes, setVotes] = useState(initialVotes);
  const [hasVoted, setHasVoted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingVoteStatus, setIsCheckingVoteStatus] = useState(false); // Don't auto-check

  // Only check vote status when user actually interacts with the vote button
  // This prevents unnecessary API calls on page load

  const checkVoteStatus = async () => {
    try {
      setIsCheckingVoteStatus(true);
      const response = await fetch(`/api/articles/${articleId}/vote`);
      if (response.ok) {
        const data = await response.json();
        setHasVoted(data.hasVoted);
      }
    } catch (error) {
      console.error('Error checking vote status:', error);
    } finally {
      setIsCheckingVoteStatus(false);
    }
  };

  const vote = async () => {
    if (hasVoted || isLoading) return;

    try {
      setIsLoading(true);

      // Check vote status first if we haven't already
      if (!isCheckingVoteStatus) {
        await checkVoteStatus();
        if (hasVoted) {
          setIsLoading(false);
          return;
        }
      }

      const response = await fetch(`/api/articles/${articleId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVotes(data.votes);
        setHasVoted(true);
      } else if (response.status === 409) {
        // Already voted
        setHasVoted(true);
      } else {
        throw new Error('Failed to vote');
      }
    } catch (error) {
      console.error('Error voting:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const removeVote = async () => {
    if (!hasVoted || isLoading) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/articles/${articleId}/vote`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVotes(data.votes);
        setHasVoted(false);
      } else {
        throw new Error('Failed to remove vote');
      }
    } catch (error) {
      console.error('Error removing vote:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleVote = () => {
    if (hasVoted) {
      removeVote();
    } else {
      vote();
    }
  };

  return {
    votes,
    hasVoted,
    isLoading,
    isCheckingVoteStatus,
    vote,
    removeVote,
    toggleVote
  };
}
