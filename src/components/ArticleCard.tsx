"use client";

import { <PERSON>, ChevronUp, <PERSON><PERSON>, Check } from 'lucide-react';
import { useState } from 'react';
import { useBookmarks } from '@/hooks/useBookmarks';
import { useVoting } from '@/hooks/useVoting';
import { Article } from '@/app/(frontend)/utils/article';

interface ArticleCardProps {
  article: Article;
  showThumbnails: boolean;
  onClick: (article: Article) => void;
}

export default function ArticleCard({ article, showThumbnails, onClick }: ArticleCardProps) {
  const { isBookmarked, toggleBookmark } = useBookmarks();
  const { votes, hasVoted, isLoading, toggleVote } = useVoting(article.id.toString(), article.likes || 0);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleBookmarkClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleBookmark({
      id: article.id.toString(),
      title: article.title,
      slug: article.slug
    });
  };

  const handleVoteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleVote();
  };

  const handleCopyLink = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const url = `${window.location.origin}/article/${article.slug}`;
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const bookmarked = isBookmarked(article.id.toString());

  return (
    <article
      className="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer"
      onClick={() => onClick(article)}
    >
      {/* Thumbnail or Gradient Fallback */}
      {showThumbnails && (
        <div className="aspect-video w-full overflow-hidden relative">
          {article.thumbnail_key ? (
            <img
              src={`/proxy?url=${article.thumbnail_key}`}
              alt={article.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="gradient-fallback w-full h-full flex items-center justify-center">
              <div className="text-white text-lg font-semibold opacity-80">
                {article.source?.title || 'Tech News'}
              </div>
            </div>
          )}
          
          {/* Copy Link Button - Top Right */}
          <button
            onClick={handleCopyLink}
            className="absolute top-2 right-2 p-2 bg-black/50 hover:bg-black/70 rounded-full text-white transition-colors opacity-0 group-hover:opacity-100"
            title="Copy link"
          >
            {copySuccess ? (
              <Check className="w-4 h-4" />
            ) : (
              <Copy className="w-4 h-4" />
            )}
          </button>
        </div>
      )}

      <div className="p-4">
        {/* Title */}
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200 line-clamp-2">
          {article.title}
        </h2>

        {/* Summary */}
        <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm line-clamp-2">
          {article.summary}
        </p>

        {/* Metadata and Actions */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-2">
            <span>{article.source?.title}</span>
            <span>•</span>
            <span>{article.timestamp}</span>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* Vote Button */}
            <button
              onClick={handleVoteClick}
              disabled={isLoading}
              className={`flex items-center space-x-1 px-2 py-1 rounded-full transition-colors ${
                hasVoted
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={hasVoted ? 'Remove vote' : 'Vote for this article'}
            >
              <ChevronUp className={`w-4 h-4 ${hasVoted ? 'fill-current' : ''}`} />
              {votes > 0 && <span className="text-xs font-medium">{votes}</span>}
            </button>

            {/* Bookmark Button */}
            <button
              onClick={handleBookmarkClick}
              className={`p-1 rounded-full transition-colors ${
                bookmarked
                  ? 'text-yellow-500 hover:text-yellow-600'
                  : 'text-gray-400 hover:text-yellow-500'
              }`}
              title={bookmarked ? 'Remove bookmark' : 'Bookmark this article'}
            >
              <Star className={`w-4 h-4 ${bookmarked ? 'fill-current' : ''}`} />
            </button>
          </div>
        </div>
      </div>
    </article>
  );
}
