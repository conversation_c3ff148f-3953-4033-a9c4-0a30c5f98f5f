"use client";

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Grid3X3, List } from 'lucide-react';

interface TagFilterBarProps {
  activeFilter: string;
  setActiveFilter: (filter: string) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  showThumbnails: boolean;
  setShowThumbnails: (show: boolean) => void;
}

export default function TagFilterBar({
  activeFilter,
  setActiveFilter,
  selectedTags,
  setSelectedTags,
  showThumbnails,
  setShowThumbnails
}: TagFilterBarProps) {
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [showAllTags, setShowAllTags] = useState(false);
  const [loading, setLoading] = useState(true);

  // Main filter buttons (excluding Timeline which will be moved to the right)
  const mainFilters = ['Recent', 'Trending'];

  // Number of tags to show initially
  const INITIAL_TAG_COUNT = 6;

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/tags');
      const tags = await response.json();
      setAvailableTags(tags);
    } catch (error) {
      console.error('Error fetching tags:', error);
      // Fallback tags
      setAvailableTags(['AI', 'Web Development', 'Cybersecurity', 'Hardware', 'Mobile']);
    } finally {
      setLoading(false);
    }
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(
      selectedTags.includes(tag)
        ? selectedTags.filter(t => t !== tag)
        : [...selectedTags, tag]
    );
  };

  const visibleTags = showAllTags ? availableTags : availableTags.slice(0, INITIAL_TAG_COUNT);
  const hasMoreTags = availableTags.length > INITIAL_TAG_COUNT;

  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 px-6 py-4">
      <div className="max-w-[2000px] mx-auto">
        {/* Main Filter Buttons and View Toggle */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex gap-3">
            {mainFilters.map((filter) => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                disabled={filter === 'Timeline'} // Timeline is disabled for now
                className={`filter-button ${
                  activeFilter === filter ? 'active' : ''
                } ${filter === 'Timeline' ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {filter}
              </button>
            ))}
          </div>

          {/* Timeline Button and View Toggle */}
          <div className="flex items-center gap-3">
            {/* Timeline Button */}
            <button
              onClick={() => setActiveFilter('Timeline')}
              disabled={true} // Timeline is disabled for now
              className={`px-4 py-2 rounded-lg font-medium transition-all border border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed opacity-50`}
            >
              Timeline
            </button>

            {/* View Toggle */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowThumbnails(true)}
                className={`p-2 rounded-lg transition-colors ${
                  showThumbnails
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
                title="Grid view"
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setShowThumbnails(false)}
                className={`p-2 rounded-lg transition-colors ${
                  !showThumbnails
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
                title="List view"
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Tag Buttons */}
        <div className="flex flex-wrap gap-2 items-center">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"
              />
            ))
          ) : (
            <>
              {visibleTags.map((tag) => (
                <button
                  key={tag}
                  onClick={() => handleTagToggle(tag)}
                  className={`tag-button ${
                    selectedTags.includes(tag) ? 'active' : ''
                  }`}
                >
                  {tag}
                </button>
              ))}

              {/* Show All/Less Button */}
              {hasMoreTags && (
                <button
                  onClick={() => setShowAllTags(!showAllTags)}
                  className="flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  {showAllTags ? (
                    <>
                      Less <ChevronUp className="w-4 h-4" />
                    </>
                  ) : (
                    <>
                      All <ChevronDown className="w-4 h-4" />
                    </>
                  )}
                </button>
              )}
            </>
          )}
        </div>

        {/* Selected Tags Summary */}
        {selectedTags.length > 0 && (
          <div className="mt-3 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Filtering by:</span>
            <div className="flex gap-1">
              {selectedTags.map((tag, index) => (
                <span key={tag}>
                  {tag}
                  {index < selectedTags.length - 1 && ', '}
                </span>
              ))}
            </div>
            <button
              onClick={() => setSelectedTags([])}
              className="text-blue-600 dark:text-blue-400 hover:underline ml-2"
            >
              Clear all
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
