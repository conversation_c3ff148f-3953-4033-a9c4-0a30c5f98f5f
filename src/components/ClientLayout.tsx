"use client";

import { ReactNode } from 'react';
import ThemeToggle from './ThemeToggle';
import UserMenu from './UserMenu';
import ClientSidebar from './ClientSidebar';

export default function ClientLayout({ children }: { children: ReactNode }) {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Sidebar */}
            <ClientSidebar />

            {/* Header */}
            <header className="h-16 fixed top-0 left-0 right-0 md:right-64 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 z-10">
                <div className="flex items-center justify-between h-full px-6">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tech News Feed</h1>
                    <div className="flex items-center space-x-4">
                        <ThemeToggle/>
                        <UserMenu/>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="pt-16 md:mr-64">
                {children}
            </main>
        </div>
    );
}