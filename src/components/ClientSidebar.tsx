import { useState, useEffect } from 'react';
import { sources, trendingTopics } from '../data/news';
import { Hash, Menu, X } from 'lucide-react';

export default function ClientSidebar() {
    // Start with false to prevent flash on mobile - will be set correctly after hydration
    const [isOpen, setIsOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(true); // Assume mobile initially to prevent flash

    useEffect(() => {

        const checkScreenSize = () => {
            const mobile = window.innerWidth < 768;
            setIsMobile(mobile);

            if (mobile) {
                setIsOpen(false);
            } else {
                setIsOpen(true);
            }
        };

        // Initialize
        checkScreenSize();

        // Add event listener
        window.addEventListener('resize', checkScreenSize);

        // Dispatch custom event when sidebar state changes
        const dispatchSidebarToggle = () => {
            window.dispatchEvent(
                new CustomEvent('sidebarToggle', { detail: { isOpen } })
            );
        };

        dispatchSidebarToggle();

        return () => {
            window.removeEventListener('resize', checkScreenSize);
        };
    }, [isOpen]);

    const toggleSidebar = () => {
        setIsOpen(!isOpen);
    };

            {/* Toggle button for mobile */}
            <button
                onClick={toggleSidebar}
                className="md:hidden fixed top-4 right-4 z-50 p-2 rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
            >
                {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" /> }
            </button>

    return (
        <>
            {/* Overlay for mobile when sidebar is open */}
            {isMobile && isOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-30"
                    onClick={toggleSidebar}
                />
            )}

            {/* Sidebar */}
            <aside
                className={`w-64 h-screen fixed right-0 top-0 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-800 p-4 overflow-y-auto z-40 transition-transform duration-300 ease-in-out sidebar-mobile-hidden ${
                    isOpen ? 'translate-x-0' : 'translate-x-full'
                } md:translate-x-0`}
            >
                <div className="space-y-8 pt-8 md:pt-0">
                    {/* Sources Section */}
                    <div>
                        <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Sources</h2>
                        <ul className="space-y-2">
                            {sources.map((source) => (
                                <li key={source.id}>
                                    <button className="w-full text-left px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-colors">
                                        <span className="mr-2"><img className="h-4 float-left" src={source.iconUrl} alt=""/></span>
                                        {source.name}
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Trending Topics */}
                    <div>
                        <h2 className="text-lg font-semibold hidden mb-4 text-gray-900 dark:text-white">Trending</h2>
                        <ul className="space-y-2">
                            {trendingTopics.map((topic) => (
                                <li key={topic}>
                                    <button className="w-full text-left px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-colors">
                                        <Hash className="inline w-4 h-4 mr-2" />
                                        {topic}
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </aside>
        </>
    );
}