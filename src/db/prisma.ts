import { neonConfig } from '@neondatabase/serverless'
import { PrismaNeon } from '@prisma/adapter-neon'
import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'

dotenv.config()

// Configure Neon for better performance and faster connections
neonConfig.pipelineConnect = false
neonConfig.pipelineTLS = false

const connectionString = process.env.DATABASE_URL

if (!connectionString) {
    throw new Error('DATABASE_URL is not defined')
}

// Use direct connection string for faster connection
const adapter = new PrismaNeon({ connectionString })

export const prisma = new PrismaClient({
    adapter,
    log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'], // Reduce logging for performance
})