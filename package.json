{"name": "tech-news-feed", "type": "module", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "payload": "cross-env NODE_OPTIONS='--import tsx/esm' payload"}, "dependencies": {"@neondatabase/serverless": "^0.10.4", "@next/third-parties": "^15.2.0-canary.33", "@payloadcms/db-vercel-postgres": "^3.39.1", "@payloadcms/next": "^3.39.1", "@payloadcms/richtext-lexical": "^3.39.1", "@payloadcms/storage-vercel-blob": "^3.39.1", "@prisma/adapter-neon": "^6.11.0", "@prisma/client": "^6.11.0", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.1.1", "@vercel/analytics": "^1.4.1", "@vercel/blob": "^0.27.2", "@vercel/speed-insights": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "lucide-react": "^0.469.0", "next": "15.3.2", "payload": "^3.39.1", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.17.11", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8", "prisma": "^6.11.0", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}